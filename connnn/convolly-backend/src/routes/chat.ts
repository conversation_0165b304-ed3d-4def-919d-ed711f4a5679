import { Router } from "express";
import {
  getConversationMessages,
  getUserConversations,
} from "../controllers/chat";
import { isAuthenticated } from "../middlewares/auth";

const chatRouter = Router();

const config = {
  withUserId: true,
  skipProfileFetch: true,
  strictVerification: true,
};

chatRouter.post(
  "/user-conversations/:userId",
  isAuthenticated(config),
  getUserConversations
);

chatRouter.post(
  "/conversation-messages/:conversationId/:userId/",
  isAuthenticated(config),
  getConversationMessages
);

export default chatRouter;
