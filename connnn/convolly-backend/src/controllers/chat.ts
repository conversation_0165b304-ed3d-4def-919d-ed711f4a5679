import { Request, Response } from "express";
import Message, { IMessage } from "../models/message";
import { createErrorResponse } from "../middlewares/errorHandler";
import {
  createOkResponse,
  paginate,
  PaginateOptions,
  serializeConversation,
} from "../utils/misc";
import Conversation, { IConversation } from "../models/conversation";
import { isValidObjectId } from "../utils/validation";

export const CONVERSATION_POPULATE = [
  "participants.user",
  {
    path: "lastMessage",
    populate: {
      path: "sender",
    },
  },
];

export const getConversationMessages = async (req: Request, res: Response) => {
  try {
    const { conversationId } = req.params;

    const {
      limit,
      sortOrder = "asc",
      cursor,
    } = (req.body || {}) as Partial<PaginateOptions<IMessage>>;

    if (!isValidObjectId(conversationId)) {
      createErrorResponse(res, "Invalid conversation id", 400);
      return;
    }

    const conversation = await Conversation.find({
      _id: conversationId,
      "participants.user": req.user.id,
    });

    if (!conversation) {
      createErrorResponse(
        res,
        {
          message: "Your aren't a participants.",
        },
        403
      );

      return;
    }

    if (cursor && !isValidObjectId(cursor)) {
      createErrorResponse(
        res,
        {
          message: "Invalid cursor id",
          details: {
            reason: "body.lastCursor",
          },
        },
        400
      );
      return;
    }

    const data = await paginate<IMessage>({
      model: Message,
      query: { conversation: conversationId },
      limit,
      sortOrder,
      cursor,
      populate: "sender",
    });

    createOkResponse(res, data);
  } catch (err: any) {
    createErrorResponse(res, err);
    return;
  }
};

export const getUserConversations = async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;

    const {
      limit,
      sortOrder = "desc",
      cursor,
    } = (req.body || {}) as Partial<PaginateOptions<IConversation>>;

    if (!isValidObjectId(userId)) {
      createErrorResponse(res, "Invalid user id", 400);
      return;
    }

    if (cursor && !isValidObjectId(cursor)) {
      createErrorResponse(
        res,
        {
          message: "Invalid cursor id",
          details: {
            reason: "body.lastCursor",
          },
        },
        400
      );
      return;
    }

    const data = await paginate<IConversation>({
      model: Conversation,
      query: {
        "participants.user": userId,
      },
      limit,
      sortOrder,
      cursor,
      sortBy: "updatedAt",
      populate: CONVERSATION_POPULATE,
    });

    const serializedData = [];

    for (const c of data.data) {
      serializedData.push(serializeConversation(c));
    }

    data.data = serializedData;

    createOkResponse(res, data);
  } catch (err: any) {
    createErrorResponse(res, err);
    return;
  }
};
