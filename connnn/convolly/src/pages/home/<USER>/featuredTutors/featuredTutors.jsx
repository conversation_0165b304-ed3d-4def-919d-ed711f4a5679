import React from "react";
import { Badge } from "../../../../components/badge/badge";
import { <PERSON><PERSON> } from "../../../../components/button/button";
import { Card, CardContent } from "../../../../components/card/card";
import fullStar from "../../../../assets/svgs/fullStar.svg";
import halfStar from "../../../../assets/svgs/halfStar.svg";
import tutor1 from "../../../../assets/images/tutor1.png";
import tutor2 from "../../../../assets/images/tutor2.png";
import tutor3 from "../../../../assets/images/tutor3.png";
import tutor4 from "../../../../assets/images/tutor4.png";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay } from "swiper/modules";
import "swiper/css";

const tutors = [
  {
    id: 1,
    name: "<PERSON>",
    category: "Finance",
    languages: "English, French, Spanish",
    rating: 4.5,
    reviews: 128,
    hourlyRate: 45,
    image: tutor1
  },
  {
    id: 2,
    name: "<PERSON>",
    category: "Technology",
    languages: "Mandarin, French, Japanese",
    rating: 5.0,
    reviews: 97,
    hourlyRate: 50,
    image: tutor2
  },
  {
    id: 3,
    name: "Elena Rodriguez",
    category: "Legal",
    languages: "Spanish, English, Portuguese",
    rating: 4.0,
    reviews: 76,
    hourlyRate: 55,
    image: tutor3
  },
  {
    id: 4,
    name: "Thomas Weber",
    category: "Healthcare",
    languages: "German, English, Freanch",
    rating: 4.5,
    reviews: 112,
    hourlyRate: 48,
    image: tutor4
  },
  {
    id: 6,
    name: "Thomas Weber",
    category: "Healthcare",
    languages: "German, English, Freanch",
    rating: 4.5,
    reviews: 112,
    hourlyRate: 48,
    image: tutor4
  },
  {
    id: 5,
    name: "Thomas Weber",
    category: "Healthcare",
    languages: "German, English, Freanch",
    rating: 4.5,
    reviews: 112,
    hourlyRate: 48,
    image: tutor4
  }
];

export const FeaturedTutors = () => {
  const renderStars = (rating) => {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    return (
      <div className="inline-flex items-center">
        {[...Array(fullStars)].map((_, i) => (
          <img key={i} className="w-[16.67px] h-4" alt="Star" src={fullStar} />
        ))}
        {hasHalfStar && (
          <img
            className="w-[15.23px] h-[14.48px]"
            alt="Half star"
            src={halfStar}
          />
        )}
      </div>
    );
  };

  return (
    <section className="w-full py-[50px] sm:px-8 px-3 bg-gray-50">
      <div className="flex flex-col gap-12 max-w-[1440px] mx-auto">
        <h2 className="font-semibold text-secondary text-[30px] tracking-tight leading-[44px] sm:text-[46px]">
          Featured Tutors
        </h2>

        <Swiper
          modules={[Autoplay]}
          slidesPerView={4}
          spaceBetween={30}
          loop={true}
          speed={9500}
          allowTouchMove:true
          autoplay={{
            delay: 0,
            disableOnInteraction: false,
            reverseDirection: false
          }}
          className="w-full smooth-scroll-slider"
          // Add responsive breakpoints for mobile view
          breakpoints={{
            // when window width is >= 320px (mobile)
            320: {
              slidesPerView: 1,
              spaceBetween: 20
            },
            // when window width is >= 640px (tablet)
            640: {
              slidesPerView: 2,
              spaceBetween: 20
            },
            // when window width is >= 768px (desktop)
            768: {
              slidesPerView: 4,
              spaceBetween: 30
            }
          }}
        >
          {tutors.map((tutor) => (
            <SwiperSlide key={tutor.id}>
              <Card className="bg-white rounded-lg group overflow-hidden">
                <CardContent className="p-0">
                  <div className="flex flex-col gap-3">
                    <div
                      className="w-full h-[166px] rounded bg-cover bg-center object-cover group-hover:scale-105 transition-all duration-300"
                      style={{ backgroundImage: `url(${tutor.image})` }}
                    />

                    <div className="flex flex-col gap-8 p-[15px]">
                      <div className="flex flex-col gap-3">
                        <div className="flex flex-col gap-2">
                          <div className="flex items-start justify-between">
                            <h3 className="flex-1 h-7 font-semibold text-secondary text-[18px] leading-[24px] tracking-normal whitespace-nowrap">
                              {tutor.name}
                            </h3>

                            <Badge className="bg-white text-primary rounded-full px-2 py-1 text-[14px] font-medium leading-[20px] tracking-normal">
                              {tutor.category}
                            </Badge>
                          </div>

                          <p className="text-gray-600 text-[16px] leading-[24px] font-normal tracking-normal">
                            {tutor.languages}
                          </p>
                        </div>

                        <div className="flex items-center gap-3">
                          {renderStars(tutor.rating)}
                          <span className="flex-1 text-gray-600 text-[16px] leading-[24px] font-normal tracking-normal">
                            {tutor.rating.toFixed(1)} ({tutor.reviews} reviews)
                          </span>
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <p className="font-semibold text-secondary text-[18px] leading-[24px] tracking-normal">
                          ${tutor.hourlyRate}/hour
                        </p>

                        <Button className="bg-primary text-white rounded-lg px-4 py-2 text-[16px] font-normal leading-[24px] tracking-normal">
                          Book Trial
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
    </section>
  );
};
