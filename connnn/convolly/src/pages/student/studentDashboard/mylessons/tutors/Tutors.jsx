import React from "react";
import useGet from "@/hooks/useGet";
import { useGetTutorsQuery } from "@/redux/slices/student/findTutorApiSlice";
import { useNavigate } from "react-router-dom";
import Loader from "@/components/loader/loader";
import { capitalizeWords } from "@/utils/utils";
import userVector from "@/assets/svgs/userVector.svg";
import { useSelector } from "react-redux";

const Tutors = () => {
	const navigate = useNavigate();

	const { data: tutors, isLoading } = useGet(useGetTutorsQuery, "");

	const hasUsedFreeTrial = useSelector(
		(state) => state?.app?.userInfo?.user?.hasUsedFreeTrial
	);

	console.log(hasUsedFreeTrial);

	return (
		<div className="">
			{isLoading && <Loader />}

			{tutors
				?.filter((tutor) => tutor.approvalStatus === "approved")
				?.map((tutor, index) => (
					<div className="border rounded-md p-4 mb-4" key={index}>
						{/* Top row for tutor info and price (always visible) */}
						<div className="flex justify-between items-center mb-3 sm:mb-0">
							<div className="flex items-center">
								<img
									src={tutor.image || userVector}
									alt=""
									className="object-cover w-12 h-12 sm:w-14 sm:h-14 mr-4 sm:mr-6 rounded-md"
								/>
								<div>
									<p className="text-[#1A1A40] text-sm sm:text-md font-semibold">
										{capitalizeWords(tutor.fullname)}
									</p>
									<p className="text-xs sm:text-sm text-[#4B5563]">
										Teaches English
									</p>
								</div>
							</div>

							<div className="text-center">
								<p className="text-[#1A1A40] text-sm sm:text-md font-semibold">
									$ {tutor.basePrice}
								</p>
								<p className="text-[#4B5563] text-xs sm:text-sm">per lesson</p>
							</div>
							<div className="hidden sm:block">
								<button
									className={`p-2 px-4 rounded-md min-w-[130px] text-sm ${
										!hasUsedFreeTrial
											? "bg-white text-primary border border-primary"
											: "bg-primary text-white"
									}`}
									onClick={
										!hasUsedFreeTrial
											? () => navigate(`/student/my-lessons/tutors/${tutor.id}`)
											: () =>
													navigate(
														`/student/my-lessons/tutors/${tutor.id}/subscribe`
													)
									}
								>
									{!hasUsedFreeTrial ? "Schedule free trial" : "Subscribe"}
								</button>
							</div>
						</div>

						{/* Button row (full width on mobile, auto width on desktop) */}
						<div className="sm:hidden mt-3">
							<button
								className={`w-full py-2 rounded-md text-sm ${
									!hasUsedFreeTrial
										? "bg-white text-primary border border-primary"
										: "bg-primary text-white"
								}`}
								onClick={
									!hasUsedFreeTrial
										? () => navigate(`/student/my-lessons/tutors/${tutor.id}`)
										: () =>
												navigate(
													`/student/my-lessons/tutors/${tutor.id}/subscribe`
												)
								}
							>
								{!hasUsedFreeTrial ? "Schedule free trial" : "Subscribe"}
							</button>
						</div>
					</div>
				))}
		</div>
	);
};

export default Tutors;
