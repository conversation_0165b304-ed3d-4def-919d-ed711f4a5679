import { X } from "lucide-react";
import React from "react";
import DeleteReviewModal from "./DeleteReviewModal";
import EditReviewModal from "./EditReviewModal";

const EditOrDeleteReviewModal = ({ review, onClose, onUpdate }) => {
	const [showDeleteModal, setShowDeleteModal] = React.useState(false);
	const [showEditModal, setShowEditModal] = React.useState(false);

	const handleShowDeleteModal = () => {
		setShowDeleteModal(true);
		onClose(); // Close the options menu
	};

	const handleShowEditModal = () => {
		setShowEditModal(true);
		onClose(); // Close the options menu
	};

	const closeModal = () => {
		setShowDeleteModal(false);
		setShowEditModal(false);
	};

	return (
		<div className="absolute right-0 mt-2 w-24 bg-white border rounded-lg shadow-lg z-50">
			<div className="flex text-xs flex-col p-2">
				<div className="mt-2 space-y-2">
					<button
						onClick={handleShowEditModal}
						className="text-[#1A1A40] w-full items-center border p-2 sm:p-4 rounded-md"
					>
						Edit
					</button>
					<button
						onClick={handleShowDeleteModal}
						className="text-[#1A1A40] w-full items-center border p-2 sm:p-4 rounded-md"
					>
						Delete
					</button>
				</div>
				{showDeleteModal && (
					<div className="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-30">
						<DeleteReviewModal
							review={review}
							onClose={closeModal}
							onUpdate={onUpdate}
						/>
					</div>
				)}

				{showEditModal && (
					<div className="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-30">
						<EditReviewModal
							review={review}
							onClose={closeModal}
							onUpdate={onUpdate}
						/>
					</div>
				)}
			</div>
		</div>
	);
};

export default EditOrDeleteReviewModal;
