import React, { useState } from "react";
import { Star } from "lucide-react";
import defaultAvatar from "../../../../../assets/images/tutor1.png";
import ReviewButton from "./ReviewButton";

const ReviewCard = ({ review, onUpdate }) => {
	const [showOptions, setShowOptions] = useState(false);

	// Extract data from review object
	const targetUser = review?.targetUser || {};
	const createdAt = review?.createdAt ? new Date(review.createdAt) : new Date();
	const rating = review?.rating || 0;
	const comment = review?.comment || "";

	// Format date
	const formatDate = (date) => {
		return date.toLocaleDateString('en-US', {
			year: 'numeric',
			month: '2-digit',
			day: '2-digit'
		});
	};

	// Render star rating
	const renderStars = (rating) => {
		return Array.from({ length: 5 }, (_, index) => (
			<Star
				key={index}
				size={16}
				className={`${
					index < rating
						? 'fill-yellow-400 text-yellow-400'
						: 'fill-gray-200 text-gray-200'
				}`}
			/>
		));
	};

	const handleShowOptions = () => {
		setShowOptions(true);
	};

	const hideOption = () => {
		setShowOptions(false);
	};

	return (
		<div className="my-2 pb-2 border-b">
			{/* Desktop Layout */}
			<div className="hidden sm:flex justify-between flex-row pr-4">
				<div className="flex justify-between flex-row">
					<div className="flex pr-6 w-[220px]">
						<img
							src={targetUser.profilePicture || defaultAvatar}
							alt={targetUser.fullname || "Tutor"}
							className="object-cover w-16 h-16 mr-6 rounded-md"
						/>
						<div className="mt-1">
							<p className="text-[#1A1A40] text-md font-semibold">
								{targetUser.fullname || "Unknown Tutor"}
							</p>
							<div className="flex flex-row">
								<p className="text-sm text-[#4B5563]">
									Total Reviews: {targetUser.reviewStats?.totalReview || 0}
								</p>
							</div>
						</div>
					</div>

					{/* rating */}
					<div className="flex flex-col w-[450px]">
						<div className="flex gap-5 flex-row py-2">
							<div className="flex items-center gap-1">
								{renderStars(rating)}
							</div>
							<p className="text-[16px] text-[#4B5563]">
								{formatDate(createdAt)}
							</p>
						</div>

						<p className="text-[#4B5563] text-sm w-100">
							{comment || "No comment provided."}
						</p>
					</div>
				</div>

				<div onClick={(e) => e.stopPropagation()} className="relative">
					<ReviewButton
						review={review}
						onUpdate={onUpdate}
					/>
				</div>
			</div>

			{/* Mobile Layout */}
			<div className="block sm:hidden">
				<div className="flex items-start justify-between mb-3">
					<div className="flex items-start">
						<img
							src={targetUser.profilePicture || defaultAvatar}
							alt={targetUser.fullname || "Tutor"}
							className="object-cover w-12 h-12 mr-3 rounded-md flex-shrink-0"
						/>
						<div className="min-w-0 flex-1">
							<p className="text-[#1A1A40] text-sm font-semibold leading-tight">
								{targetUser.fullname || "Unknown Tutor"}
							</p>
							<p className="text-xs text-[#4B5563]">
								Total Reviews: {targetUser.reviewStats?.totalReview || 0}
							</p>
						</div>
					</div>
					<div className="relative">
						<ReviewButton
							review={review}
							onUpdate={onUpdate}
						/>
					</div>
				</div>

				{/* Rating and date */}
				<div className="flex items-center justify-between mb-2">
					<div className="flex items-center gap-1">
						{renderStars(rating)}
					</div>
					<p className="text-xs text-[#4B5563]">
						{formatDate(createdAt)}
					</p>
				</div>

				{/* Review text */}
				<p className="text-[#4B5563] text-xs leading-relaxed">
					{comment || "No comment provided."}
				</p>
			</div>
		</div>
	);
};

export default ReviewCard;
