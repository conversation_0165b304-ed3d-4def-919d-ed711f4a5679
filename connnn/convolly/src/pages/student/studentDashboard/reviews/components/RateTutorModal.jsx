import { <PERSON>, <PERSON> } from "lucide-react";
import React, { useState } from "react";
import { Button } from "@/components/button/button";
import { useCreateReviewMutation } from "@/redux/slices/student/reviewApiSlice";
import { useToast } from "@/context/toastContext/toastContext";

const RateTutorModal = ({
	onClose,
	tutorName,
	tutorId,
	lessonId,
	onSubmitReview
}) => {
	const [rating, setRating] = useState(0);
	const [comment, setComment] = useState("");
	const [hoverRating, setHoverRating] = useState(0);
	const [isSubmitting, setIsSubmitting] = useState(false);

	const [createReview] = useCreateReviewMutation();
	const { showToast } = useToast();

	const handleSubmit = async () => {
		if (rating === 0) {
			showToast("Please select a rating", "error");
			return;
		}

		if (!comment.trim()) {
			showToast("Please provide a comment", "error");
			return;
		}

		setIsSubmitting(true);

		try {
			await createReview({
				targetId: tutorId,
				targetRole: "tutor",
				rating,
				comment: comment.trim(),
				lessonId,
			}).unwrap();

			showToast(`Review for ${tutorName} submitted successfully!`, "success");

			// Call the callback if provided (for backward compatibility)
			if (onSubmitReview) {
				onSubmitReview({ rating, comment });
			}

			onClose();
		} catch (error) {
			console.error("Error creating review:", error);
			showToast(
				error?.data?.message || "Failed to submit review. Please try again.",
				"error"
			);
		} finally {
			setIsSubmitting(false);
		}
	};

	const renderStars = () => {
		return Array.from({ length: 5 }, (_, index) => {
			const starValue = index + 1;
			const isActive = starValue <= (hoverRating || rating);

			return (
				<Star
					key={index}
					size={32}
					className={`cursor-pointer transition-colors ${
						isActive
							? 'fill-yellow-400 text-yellow-400'
							: 'fill-gray-200 text-gray-200 hover:fill-yellow-300 hover:text-yellow-300'
					}`}
					onClick={() => setRating(starValue)}
					onMouseEnter={() => setHoverRating(starValue)}
					onMouseLeave={() => setHoverRating(0)}
				/>
			);
		});
	};

	return (
		<div className="sm:w-[669px] w-sm mx-auto p-2 sm:p-6 bg-white rounded-lg shadow-lg">
			<div className="flex gap-[145px] pt-2 justify-between">
				<h2 className="text-2xl font-bold text-[#1A1A40]">Rate Your Experience</h2>
				<button
					onClick={onClose}
					disabled={isSubmitting}
					className="text-gray-500 hover:text-gray-700"
				>
					<X />
				</button>
			</div>

			<div className="my-2">
				<div className="items-center justify-center text-center">
					<div>
						<h1 className="text-xl sm:text-[30px] font-semibold text-[#1A1A40] pb-2">
							Rate your experience
						</h1>
						<p className="text-sm sm:text-[18px] text-[#4B5563]">
							We highly value your feedback! Kindly take a moment to rate your
							experience with {tutorName} and provide us with your valuable
							feedback
						</p>
						<br />
						<div className="flex items-center justify-center gap-5 flex-row pr-4">
							<div className="flex space-x-2">
								{renderStars()}
							</div>
						</div>
					</div>
				</div>
				<div>
					<div className="mt-6">
						<label className="block text-[#1A1A40] text-md font-medium mb-2">
							Comment
						</label>
						<textarea
							className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
							rows={6}
							placeholder="Tell us about your experience"
							value={comment}
							onChange={(e) => setComment(e.target.value)}
							disabled={isSubmitting}
							required
						/>
					</div>
				</div>
				<Button
					className="mt-4 w-full h-[50px]"
					onClick={handleSubmit}
					disabled={isSubmitting}
				>
					{isSubmitting ? "Submitting..." : "Submit review"}
				</Button>
			</div>
		</div>
	);
};

export default RateTutorModal;
