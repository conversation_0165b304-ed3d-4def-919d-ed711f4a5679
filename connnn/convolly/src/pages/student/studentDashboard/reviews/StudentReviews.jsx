import React, { useState, useEffect } from "react";
import { useSelector } from "react-redux";
import { Search, Filter, ChevronDown } from "lucide-react";
import ReviewCard from "./components/ReviewCard";
import { useGetMyReviewsMutation } from "@/redux/slices/student/reviewApiSlice";
import Loader from "@/components/loader/loader";

const StudentReviews = () => {
	const [reviews, setReviews] = useState([]);
	const [page, setPage] = useState(1);
	const [hasMore, setHasMore] = useState(true);
	const [loading, setLoading] = useState(false);
	const [searchTerm, setSearchTerm] = useState("");
	const [sortOrder, setSortOrder] = useState("desc");
	const [ratingFilter, setRatingFilter] = useState("");
	const [showFilters, setShowFilters] = useState(false);

	// Get current user info from Redux store
	const userInfo = useSelector((state) => state.app.userInfo);
	const currentUser = userInfo?.user;

	// API mutation hook
	const [getMyReviews, { isLoading, error }] = useGetMyReviewsMutation();

	// Fetch reviews on component mount and when filters change
	useEffect(() => {
		if (currentUser?.id) {
			fetchReviews();
		}
	}, [currentUser?.id, sortOrder, ratingFilter]);

	// Debounced search effect
	useEffect(() => {
		const timeoutId = setTimeout(() => {
			if (currentUser?.id) {
				fetchReviews(1, true);
			}
		}, 500);

		return () => clearTimeout(timeoutId);
	}, [searchTerm]);

	const fetchReviews = async (pageNum = 1, reset = true) => {
		if (!currentUser?.id) return;

		setLoading(true);
		try {
			const response = await getMyReviews({
				userId: currentUser.id,
				page: pageNum,
				limit: 10,
				sortOrder: sortOrder
			}).unwrap();

			if (response?.data) {
				let newReviews = response.data;

				// Apply client-side filtering
				if (ratingFilter) {
					newReviews = newReviews.filter(review =>
						review.rating === parseInt(ratingFilter)
					);
				}

				if (searchTerm) {
					newReviews = newReviews.filter(review =>
						review.targetUser?.fullname?.toLowerCase().includes(searchTerm.toLowerCase()) ||
						review.comment?.toLowerCase().includes(searchTerm.toLowerCase())
					);
				}

				if (reset) {
					setReviews(newReviews);
				} else {
					setReviews(prev => [...prev, ...newReviews]);
				}

				// Check if there are more reviews to load
				setHasMore(response.data.length === 10);
				setPage(pageNum);
			}
		} catch (err) {
			console.error("Error fetching reviews:", err);
		} finally {
			setLoading(false);
		}
	};

	const loadMoreReviews = () => {
		if (!loading && hasMore) {
			fetchReviews(page + 1, false);
		}
	};

	const handleReviewUpdate = () => {
		// Refresh reviews after update/delete
		fetchReviews(1, true);
	};

	if (!currentUser) {
		return (
			<div className="flex justify-center items-center h-64">
				<p className="text-gray-500">Please log in to view your reviews.</p>
			</div>
		);
	}

	if (isLoading && reviews.length === 0) {
		return (
			<div className="flex justify-center items-center h-64">
				<Loader />
			</div>
		);
	}

	if (error) {
		return (
			<div className="flex justify-center items-center h-64">
				<p className="text-red-500">Error loading reviews. Please try again.</p>
			</div>
		);
	}

	return (
		<div className="space-y-4">
			{/* Search and Filter Controls */}
			<div className="bg-white p-4 rounded-lg shadow-sm border">
				<div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
					{/* Search Bar */}
					<div className="relative flex-1 max-w-md">
						<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
						<input
							type="text"
							placeholder="Search reviews by tutor name or comment..."
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
						/>
					</div>

					{/* Filter Controls */}
					<div className="flex gap-2 items-center">
						<button
							onClick={() => setShowFilters(!showFilters)}
							className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
						>
							<Filter size={16} />
							Filters
							<ChevronDown
								size={16}
								className={`transform transition-transform ${showFilters ? 'rotate-180' : ''}`}
							/>
						</button>
					</div>
				</div>

				{/* Expanded Filters */}
				{showFilters && (
					<div className="mt-4 pt-4 border-t border-gray-200">
						<div className="flex flex-col sm:flex-row gap-4">
							{/* Sort Order */}
							<div className="flex flex-col">
								<label className="text-sm font-medium text-gray-700 mb-1">Sort by Date</label>
								<select
									value={sortOrder}
									onChange={(e) => setSortOrder(e.target.value)}
									className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
								>
									<option value="desc">Newest First</option>
									<option value="asc">Oldest First</option>
								</select>
							</div>

							{/* Rating Filter */}
							<div className="flex flex-col">
								<label className="text-sm font-medium text-gray-700 mb-1">Filter by Rating</label>
								<select
									value={ratingFilter}
									onChange={(e) => setRatingFilter(e.target.value)}
									className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
								>
									<option value="">All Ratings</option>
									<option value="5">5 Stars</option>
									<option value="4">4 Stars</option>
									<option value="3">3 Stars</option>
									<option value="2">2 Stars</option>
									<option value="1">1 Star</option>
								</select>
							</div>

							{/* Clear Filters */}
							<div className="flex flex-col justify-end">
								<button
									onClick={() => {
										setSearchTerm("");
										setSortOrder("desc");
										setRatingFilter("");
									}}
									className="px-4 py-2 text-gray-600 hover:text-gray-800 underline"
								>
									Clear Filters
								</button>
							</div>
						</div>
					</div>
				)}
			</div>

			{/* Reviews List */}
			{isLoading && reviews.length === 0 ? (
				<div className="flex justify-center items-center h-64">
					<Loader />
				</div>
			) : error ? (
				<div className="flex justify-center items-center h-64">
					<p className="text-red-500">Error loading reviews. Please try again.</p>
				</div>
			) : reviews.length === 0 ? (
				<div className="text-center py-12">
					<p className="text-gray-500 text-lg">
						{searchTerm || ratingFilter ? "No reviews match your filters." : "You haven't written any reviews yet."}
					</p>
					<p className="text-gray-400 text-sm mt-2">
						{searchTerm || ratingFilter ? "Try adjusting your search or filters." : "Reviews you write for tutors will appear here."}
					</p>
				</div>
			) : (
				<>
					{reviews.map((review) => (
						<ReviewCard
							key={review._id}
							review={review}
							onUpdate={handleReviewUpdate}
						/>
					))}

					{hasMore && !searchTerm && !ratingFilter && (
						<div className="flex justify-center mt-6">
							<button
								onClick={loadMoreReviews}
								disabled={loading}
								className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
							>
								{loading ? "Loading..." : "Load More"}
							</button>
						</div>
					)}
				</>
			)}
		</div>
	);
};

export default StudentReviews;
