import React, { useState } from "react";
import img from "@/assets/images/tutor1.png";
import tick from "@/assets/images/studentDashboard/tickIcon.png";
import CancelSubscriptionModal from "./CancelSubscriptionModal";
import RefundRequestModal from "./RefundRequestModal";
import PauseSubscriptionModal from "./PauseSubscriptionModal";

const SubsCard = ({ subscription }) => {
	const [showCancelModal, setShowCancelModal] = useState(false);
	const [showRefundModal, setShowRefundModal] = useState(false);
	const [showPauseModal, setShowPauseModal] = useState(false);

	const handleCloseModal = () => {
		setShowCancelModal(false);
		setShowRefundModal(false);
		setShowPauseModal(false);
	};

	console.log(subscription);

	return (
		<>
			<div className="border rounded-md p-4 flex flex-col h-full">
				<div className="border-b pb-4">
					<div className="flex justify-between">
						<img
							src={img}
							className="w-8 h-8 object-cover rounded-full"
							alt=""
						/>
						<button
							onClick={() => setShowRefundModal(true)}
							className=" px-2 py-1 sm:px-4 sm:py-1 border rounded-lg border-primary hover:bg-primary hover:text-white transition-colors"
						>
							Refund
						</button>
					</div>
					<div className="flex text-[#4B5563] text-sm mt-2 justify-between">
						<p>{subscription?.fullName}</p>
						<p>{subscription?.lessonsLeft} lessons left</p>
					</div>
				</div>

				<div className="mt-4 flex-grow">
					<div className="text-[#4B5563] space-y-4 text-md">
						<p className="text-[#1A1A40] text-2xl mb-2">1 lesson per week</p>
						<div className="flex">
							<img src={tick} className="h-4 w-4 mr-2 pt-1" alt="tick icon" />
							<p>That's 4 lessons every 4 weeks at $12.00</p>
						</div>
						<div className="flex">
							<img src={tick} className="h-4 w-4 mr-2 pt-1" alt="tick icon" />
							<p className="text-md">
								Schedule your 4 lessons for anytime during the 4 weeks period
							</p>
						</div>
						<div className="flex">
							<img src={tick} className="h-4 w-4 mr-2 pt-1" alt="tick icon" />
							<p>Change your tutor for free at anytime</p>
						</div>
						<div className="flex">
							<img src={tick} className="h-4 w-4 mr-2 pt-1" alt="tick icon" />
							<p>Cancel or pause subscription at anytime</p>
						</div>
						<div className="flex">
							<img src={tick} className="h-4 w-4 mr-2 pt-1" alt="tick icon" />
							<p>Change the duration of your classes at anytime</p>
						</div>
					</div>
				</div>

				<div className="mt-6 pt-4 flex justify-end space-x-2 text-md">
					<button
						onClick={() => setShowPauseModal(true)}
						className="border px-2 py-1 sm:px-4 sm:py-1 rounded-md hover:bg-gray-50 transition-colors"
					>
						Pause
					</button>
					<button
						onClick={() => setShowCancelModal(true)}
						className="border text-white  px-2 py-1 sm:px-4 sm:py-2 rounded-md bg-primary hover:bg-primary-dark transition-colors"
					>
						Cancel
					</button>
				</div>
			</div>

			{/* Modals */}
			{showCancelModal && (
				<div className="fixed inset-0 p-4 bg-black bg-opacity-50 flex items-center justify-center z-50">
					<CancelSubscriptionModal onClose={handleCloseModal} />
				</div>
			)}

			{showRefundModal && (
				<div className="fixed p-4 inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
					<RefundRequestModal onClose={handleCloseModal} />
				</div>
			)}

			{showPauseModal && (
				<div className="fixed p-4 inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
					<PauseSubscriptionModal onClose={handleCloseModal} />
				</div>
			)}
		</>
	);
};

export default SubsCard;
