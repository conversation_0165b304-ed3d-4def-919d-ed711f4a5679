import React from "react";
import SubsCard from "./components/SubsCard";
import { useGetStudentSubscriptionsQuery } from "@/redux/slices/student/subscriptionApiSlice";
import { useSelector } from "react-redux";
import Loader from "@/components/loader/loader";

const Subscription = () => {
	// Get the user ID from Redux state
	const user = useSelector((state) => state?.app?.userInfo?.user);
	const id = user?.id;

	// Call the query hook directly with the user ID
	console.log("user", user);
	console.log("id", id);

	const {
		data: subscriptions,
		isLoading,
		error,
	} = useGetStudentSubscriptionsQuery(id, {
		skip: !id,
	});

	const subsList = subscriptions;
	console.log("error", error);

	console.log("subsList", subsList);
	console.log("subsList", subscriptions);

	return (
		<div className="px-1 sm:px-4">
			<div className="text-[#4B5563] text-md sm:text-[16px] gap-2">
				<p className="py-2">
					All subscription are set to renew automatically at the end of each
					billing cycle, using your saved payment method. You will receive a
					reminder email 3 days before renewal
				</p>
				<p className="pb-2">
					If you pause your subscription for more than 90 days, your
					subscription will expire.
				</p>
			</div>

			<div className="mt-4 space-y-4">
				{isLoading ? (
					<p>
						<Loader />
					</p>
				) : error ? (
					<p className="text-red-500">Error loading subscriptions</p>
				) : subsList.length === 0 ? (
					<p>No subscriptions found.</p>
				) : (
					subsList.map((sub) => <SubsCard key={sub.id} subscription={sub} />)
				)}
				<SubsCard />
			</div>

			<div className="mt-4">
				<div className="mb-4">
					<p className="text-[#1A1A40] text-lg mb-2">Cancellation</p>
					<p className="text-[#4B5563]">
						You can cancel your subscription anytime. After cancellation, you
						will continue to have access to premium features until the end of
						your current billing period. No further charges will be made unless
						you reactivate your plan.
					</p>
				</div>
				<div>
					<p className="text-[#1A1A40] text-lg mb-2">Refund</p>
					<p className="text-[#4B5563]">
						We offer a 7-day money-back guarantee. After this period, payments
						are non-refundable.
					</p>
				</div>
			</div>
		</div>
	);
};

export default Subscription;
