import ClassSchedule from "./components/ClassSchedule";
import RecentClasses from "./components/RecentClasses";
import TotalClasses from "@/assets/svgs/studentDashboard/totalClasses";
import TotalNoOfTutorsIcon from "@/assets/svgs/studentDashboard/totalNoOfTutorsIcon";
import { useState } from "react";
import WithdrawSuccessModal from "./components/WithdrawSuccessModal";
import WithdrawModal from "./components/WithdrawModal";
import {
	useGetActiveStudentsQuery,
	useGetEarningsQuery,
	useRequestWithdrawalMutation,
} from "@/redux/slices/tutor/tutorDashboardApiSlice";
import { useGetTutorClassesQuery } from "@/redux/slices/student/classesApiSlice";
import usePost from "@/hooks/usePost";
import useGet from "@/hooks/useGet";
import Loader from "@/components/loader/loader";

const TutorDashboard = () => {
	const [showModal, setShowModal] = useState(false);
	const [showSuccessModal, setShowSuccessModal] = useState(false);

	const openModal = () => {
		setShowModal(true);
		console.log("button clicked");
	};

	const handleContinue = () => {
		setShowModal(false);
		setShowSuccessModal(true);
		refetch();
	};

	const {
		data: stats,
		isLoading: gettingStats,
		refetch,
	} = useGet(useGetEarningsQuery, "");

	const { data: activeStudents, isLoading: gettingStudents } = useGet(
		useGetActiveStudentsQuery,
		""
	);

	// Generate days and dates for the current week starting from July 10, 2025

	const today = new Date(2025, 6, 10); // July 10, 2025
	const days = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
	const dates = Array.from({ length: 7 }, (_, i) => {
		const date = new Date(today);
		date.setDate(today.getDate() + i);
		return {
			day: days[date.getDay()],
			date: date.getDate(),
			fullDate: date.toISOString().split("T")[0],
			month: date.getMonth(),
			year: date.getFullYear(),
		};
	});

	const [activeDate, setActiveDate] = useState(dates[0].date);

	// Fetch bookings data
	const { data: bookings, isLoading } = useGet(useGetTutorClassesQuery, "");

	// Filter upcoming classes for the selected date
	const upcomingClasses =
		bookings?.bookings?.filter((booking) => {
			const bookingDate = new Date(booking.scheduledTime);
			const selectedDate = dates.find((d) => d.date === activeDate);

			if (!selectedDate) return false;

			return (
				bookingDate.getDate() === selectedDate.date &&
				bookingDate.getMonth() === selectedDate.month &&
				bookingDate.getFullYear() === selectedDate.year
			);
		}) || [];

	// Calculate end time by adding duration minutes
	const getEndTime = (startTime, duration) => {
		const start = new Date(startTime);
		// Add UTC offset to get correct local time display
		const localStart = new Date(
			start.getTime() + start.getTimezoneOffset() * 60000
		);
		const end = new Date(localStart.getTime() + duration * 60000);
		return end.toLocaleTimeString("en-US", {
			hour: "numeric",
			minute: "2-digit",
			hour12: true,
		});
	};

	// Recent classes (limit to 5, sorted by date descending)
	const now = new Date();

	const recentClasses = [...(bookings?.bookings || [])]
		.filter((cls) => new Date(cls.scheduledTime) < now)
		.sort((a, b) => new Date(b.scheduledTime) - new Date(a.scheduledTime))
		.slice(0, 5);

	const cards = [
		{
			Icon: TotalNoOfTutorsIcon,
			value: stats?.data?.currentBalance?.total ?? 0,
			label: "Total Earnings",
			format: (value) => `$${parseFloat(value).toFixed(2)}`,
		},
		{
			Icon: TotalNoOfTutorsIcon,
			value: stats?.data?.currentBalance?.available ?? 0,
			label: "Current Earnings",
			Button: "Withdraw",
			format: (value) => `$${parseFloat(value).toFixed(2)}`,
		},
		{
			Icon: TotalClasses,
			value: stats?.data?.summary?.totalLessonsCompleted ?? 0,
			label: "Total no of lessons",
		},
		{
			Icon: TotalNoOfTutorsIcon,
			value: activeStudents?.data?.length ?? 0,
			label: "Total no of active students",
		},
	];

	return (
		<div className="">
			{(gettingStats || gettingStudents) && <Loader />}

			{/* Dashboard Header */}
			<div className="flex flex-col md:flex-row justify-between gap-4">
				<div>
					<p className="text-2xl md:text-[26px] mb-2 font-semibold text-[#1A1A40]">
						Welcome Back
					</p>
					<p className="text-sm md:text-lg text-[#4B5563]">
						We're excited to have you here! This is your personal space to{" "}
						<span className="hidden sm:inline">
							track progress, access lessons, and connect with your students.
						</span>
						<span className="sm:hidden">
							track progress and connect with students.
						</span>
					</p>
				</div>
			</div>

			{/* Cards Section */}
			<div className="my-6 md:my-8">
				<div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-4 gap-3 sm:gap-4 md:gap-6">
					{cards.map(({ label, Icon, value, Button, format }, index) => (
						<div
							key={index}
							className="h-[120px] sm:h-[148px] rounded-md border border-[#E8E8E8] flex flex-col justify-between p-3 sm:p-4"
						>
							<Icon className="w-6 h-6 sm:w-auto sm:h-auto" />
							<div className="flex flex-row justify-between">
								<div className="flex flex-col mt-auto">
									<p className="text-xl sm:text-[26px] text-[#1A1A40] font-bold my-1 sm:my-2">
										{format ? format(value) : value}
									</p>
									<p className="text-xs sm:text-[14px] text-[#4B5563]">
										{label}
									</p>
								</div>
								{Button && (
									<button
										onClick={() => setShowModal(true)}
										className="mt-8 sm:mt-12 hover:cursor-pointer text-white bg-[#FF9500] rounded-lg px-2 py-1 sm:px-4 sm:py-2 text-xs sm:text-base"
									>
										{Button}
									</button>
								)}
							</div>
						</div>
					))}
				</div>

				{/* Modals */}
				{showModal && (
					<div className="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-30">
						<div className="animate-slideUp w-full max-w-md mx-4">
							<WithdrawModal
								onClose={() => setShowModal(false)}
								onContinue={handleContinue}
								availableBalance={stats?.data?.currentBalance?.available}
							/>
						</div>
					</div>
				)}
				{showSuccessModal && (
					<div
						onClick={() => setShowSuccessModal(false)}
						className="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-30"
					>
						<div className="animate-slideUp w-full max-w-md mx-4">
							<WithdrawSuccessModal />
						</div>
					</div>
				)}
			</div>

			{/* Recent Classes Section */}
			<div className="w-full text-[#1A1A40] mt-6 md:mt-8">
				<p className="text-[#1A1A40] text-lg md:text-xl font-semibold">
					Recent Classes
				</p>
				<div className="gap-4 w-full">
					<RecentClasses isLoading={isLoading} recentClasses={recentClasses} />
				</div>
			</div>
		</div>
	);
};

export default TutorDashboard;
