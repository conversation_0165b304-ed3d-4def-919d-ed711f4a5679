import React, { useState } from "react";
import { Star, Flag, MessageCircle, ThumbsUp, MoreVertical, Eye } from "lucide-react";
import { useReportReviewMutation } from "@/redux/slices/tutor/tutorReviewsApiSlice";
import { useToast } from "@/context/toastContext/toastContext";
import ReportReviewModal from "./ReportReviewModal";
import ViewReviewModal from "./ViewReviewModal";

const ReviewCard = ({ review, onUpdate }) => {
	const [showOptions, setShowOptions] = useState(false);
	const [showReportModal, setShowReportModal] = useState(false);
	const [showViewModal, setShowViewModal] = useState(false);
	const [reportReview, { isLoading: isReporting }] = useReportReviewMutation();
	const { showToast } = useToast();

	// Extract data from review object
	const student = review?.createdBy || review?.student || {};
	const createdAt = review?.createdAt ? new Date(review.createdAt) : new Date();
	const rating = review?.rating || 0;
	const comment = review?.comment || "";
	const isReported = review?.flagged || false;
	const lessonInfo = review?.lesson || {};

	// Format date
	const formatDate = (date) => {
		return date.toLocaleDateString('en-US', {
			year: 'numeric',
			month: 'short',
			day: 'numeric'
		});
	};

	// Render star rating
	const renderStars = (rating) => {
		return Array.from({ length: 5 }, (_, index) => (
			<Star
				key={index}
				size={16}
				className={`${
					index < rating
						? 'fill-yellow-400 text-yellow-400'
						: 'fill-gray-200 text-gray-200'
				}`}
			/>
		));
	};

	const handleReportReview = async (reason) => {
		try {
			await reportReview({
				reviewId: review._id || review.id,
				reason
			}).unwrap();
			
			showToast("Review reported successfully. It will be reviewed by our moderation team.", "success");
			setShowReportModal(false);
			setShowOptions(false);
			
			if (onUpdate) {
				onUpdate();
			}
		} catch (error) {
			showToast(error?.data?.message || "Failed to report review", "error");
		}
	};

	const handleViewDetails = () => {
		setShowViewModal(true);
		setShowOptions(false);
	};

	return (
		<>
			<div className="bg-white border border-gray-200 rounded-lg p-4 mb-4 hover:shadow-md transition-shadow">
				{/* Desktop Layout */}
				<div className="hidden sm:flex justify-between">
					<div className="flex w-full">
						{/* Student Info */}
						<div className="flex items-start pr-6 min-w-[200px]">
							<img
								src={student?.avatar || student?.profilePicture || "/default-avatar.png"}
								alt={student?.fullname || student?.name || "Student"}
								className="object-cover w-12 h-12 rounded-full mr-3"
							/>
							<div>
								<p className="text-[#1A1A40] text-sm font-semibold">
									{student?.fullname || student?.name || "Anonymous Student"}
								</p>
								<p className="text-[#4B5563] text-xs">
									{formatDate(createdAt)}
								</p>
								{lessonInfo?.subject && (
									<p className="text-[#4B5563] text-xs">
										Subject: {lessonInfo.subject}
									</p>
								)}
							</div>
						</div>

						{/* Rating and Content */}
						<div className="flex flex-col flex-1">
							<div className="flex items-center gap-3 mb-2">
								<div className="flex items-center gap-1">
									{renderStars(rating)}
								</div>
								<span className="text-sm font-medium text-[#1A1A40]">
									{rating}/5
								</span>
								{isReported && (
									<span className="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">
										Reported
									</span>
								)}
							</div>

							<p className="text-[#4B5563] text-sm leading-relaxed mb-3">
								{comment || "No comment provided."}
							</p>

							{/* Engagement metrics */}
							<div className="flex items-center gap-4 text-xs text-[#4B5563]">
								<div className="flex items-center gap-1">
									<MessageCircle size={14} />
									<span>{review.commentsCount || 0} comments</span>
								</div>
								<div className="flex items-center gap-1">
									<ThumbsUp size={14} />
									<span>{review.likesCount || 0} likes</span>
								</div>
							</div>
						</div>
					</div>

					{/* Actions */}
					<div className="relative">
						<button
							onClick={() => setShowOptions(!showOptions)}
							className="p-2 hover:bg-gray-100 rounded-full transition-colors"
							disabled={isReporting}
						>
							<MoreVertical size={16} />
						</button>

						{showOptions && (
							<div className="absolute right-0 top-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10 min-w-[150px]">
								<button
									onClick={handleViewDetails}
									className="w-full px-4 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2"
								>
									<Eye size={14} />
									View Details
								</button>
								{!isReported && (
									<button
										onClick={() => setShowReportModal(true)}
										className="w-full px-4 py-2 text-left text-sm hover:bg-gray-50 text-red-600 flex items-center gap-2"
									>
										<Flag size={14} />
										Report Review
									</button>
								)}
							</div>
						)}
					</div>
				</div>

				{/* Mobile Layout */}
				<div className="sm:hidden">
					<div className="flex justify-between items-start mb-3">
						<div className="flex items-start">
							<img
								src={student?.avatar || student?.profilePicture || "/default-avatar.png"}
								alt={student?.fullname || student?.name || "Student"}
								className="object-cover w-10 h-10 rounded-full mr-3"
							/>
							<div>
								<p className="text-[#1A1A40] text-sm font-semibold">
									{student?.fullname || student?.name || "Anonymous Student"}
								</p>
								<p className="text-[#4B5563] text-xs">
									{formatDate(createdAt)}
								</p>
							</div>
						</div>

						<button
							onClick={() => setShowOptions(!showOptions)}
							className="p-1 hover:bg-gray-100 rounded-full"
						>
							<MoreVertical size={14} />
						</button>
					</div>

					{/* Rating */}
					<div className="flex items-center gap-2 mb-2">
						<div className="flex items-center gap-1">
							{renderStars(rating)}
						</div>
						<span className="text-sm font-medium text-[#1A1A40]">
							{rating}/5
						</span>
						{isReported && (
							<span className="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">
								Reported
							</span>
						)}
					</div>

					{/* Comment */}
					<p className="text-[#4B5563] text-sm leading-relaxed mb-3">
						{comment || "No comment provided."}
					</p>

					{/* Engagement metrics */}
					<div className="flex items-center gap-4 text-xs text-[#4B5563]">
						<div className="flex items-center gap-1">
							<MessageCircle size={12} />
							<span>{review.commentsCount || 0}</span>
						</div>
						<div className="flex items-center gap-1">
							<ThumbsUp size={12} />
							<span>{review.likesCount || 0}</span>
						</div>
					</div>

					{/* Mobile Options Menu */}
					{showOptions && (
						<div className="mt-3 border-t pt-3">
							<button
								onClick={handleViewDetails}
								className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2 rounded"
							>
								<Eye size={14} />
								View Details
							</button>
							{!isReported && (
								<button
									onClick={() => setShowReportModal(true)}
									className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 text-red-600 flex items-center gap-2 rounded"
								>
									<Flag size={14} />
									Report Review
								</button>
							)}
						</div>
					)}
				</div>
			</div>

			{/* Modals */}
			{showReportModal && (
				<ReportReviewModal
					review={review}
					onClose={() => setShowReportModal(false)}
					onReport={handleReportReview}
					isLoading={isReporting}
				/>
			)}

			{showViewModal && (
				<ViewReviewModal
					review={review}
					onClose={() => setShowViewModal(false)}
				/>
			)}

			{/* Click outside to close options */}
			{showOptions && (
				<div
					className="fixed inset-0 z-5"
					onClick={() => setShowOptions(false)}
				/>
			)}
		</>
	);
};

export default ReviewCard;
