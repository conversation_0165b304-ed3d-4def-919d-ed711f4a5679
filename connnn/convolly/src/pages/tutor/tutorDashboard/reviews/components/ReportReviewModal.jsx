import React, { useState } from "react";
import { X, Flag, AlertTriangle } from "lucide-react";
import { Button } from "@/components/button/button";

const ReportReviewModal = ({ review, onClose, onReport, isLoading }) => {
	const [selectedReason, setSelectedReason] = useState("");
	const [customReason, setCustomReason] = useState("");
	const [showCustomInput, setShowCustomInput] = useState(false);

	const reportReasons = [
		{
			id: "inappropriate_language",
			label: "Inappropriate Language",
			description: "Contains offensive, abusive, or inappropriate language"
		},
		{
			id: "false_information",
			label: "False Information",
			description: "Contains false or misleading information about the lesson"
		},
		{
			id: "spam",
			label: "Spam or Irrelevant",
			description: "Review is spam or not related to the actual lesson"
		},
		{
			id: "personal_attack",
			label: "Personal Attack",
			description: "Contains personal attacks or harassment"
		},
		{
			id: "fake_review",
			label: "Fake Review",
			description: "Appears to be a fake or fraudulent review"
		},
		{
			id: "other",
			label: "Other",
			description: "Other reason not listed above"
		}
	];

	const handleReasonSelect = (reasonId) => {
		setSelectedReason(reasonId);
		setShowCustomInput(reasonId === "other");
		if (reasonId !== "other") {
			setCustomReason("");
		}
	};

	const handleSubmit = () => {
		if (!selectedReason) return;

		const reason = selectedReason === "other" ? customReason : selectedReason;
		if (!reason.trim()) return;

		onReport(reason);
	};

	const isSubmitDisabled = !selectedReason || 
		(selectedReason === "other" && !customReason.trim()) || 
		isLoading;

	return (
		<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
			<div className="bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto">
				{/* Header */}
				<div className="flex items-center justify-between p-6 border-b">
					<div className="flex items-center gap-3">
						<div className="p-2 bg-red-100 rounded-full">
							<Flag className="w-5 h-5 text-red-600" />
						</div>
						<div>
							<h2 className="text-lg font-semibold text-gray-900">
								Report Review
							</h2>
							<p className="text-sm text-gray-500">
								Help us maintain quality by reporting inappropriate content
							</p>
						</div>
					</div>
					<button
						onClick={onClose}
						className="p-2 hover:bg-gray-100 rounded-full transition-colors"
						disabled={isLoading}
					>
						<X className="w-5 h-5" />
					</button>
				</div>

				{/* Review Preview */}
				<div className="p-6 bg-gray-50 border-b">
					<div className="flex items-start gap-3">
						<img
							src={review?.createdBy?.avatar || review?.student?.avatar || "/default-avatar.png"}
							alt="Student"
							className="w-10 h-10 rounded-full object-cover"
						/>
						<div className="flex-1">
							<p className="font-medium text-gray-900">
								{review?.createdBy?.fullname || review?.student?.name || "Anonymous Student"}
							</p>
							<div className="flex items-center gap-1 mt-1">
								{[...Array(5)].map((_, i) => (
									<div
										key={i}
										className={`w-3 h-3 rounded-full ${
											i < (review?.rating || 0) ? 'bg-yellow-400' : 'bg-gray-200'
										}`}
									/>
								))}
								<span className="text-sm text-gray-500 ml-1">
									{review?.rating || 0}/5
								</span>
							</div>
							<p className="text-sm text-gray-600 mt-2 line-clamp-3">
								{review?.comment || "No comment provided."}
							</p>
						</div>
					</div>
				</div>

				{/* Report Reasons */}
				<div className="p-6">
					<h3 className="text-sm font-medium text-gray-900 mb-4">
						Why are you reporting this review?
					</h3>
					
					<div className="space-y-3">
						{reportReasons.map((reason) => (
							<label
								key={reason.id}
								className={`block p-3 border rounded-lg cursor-pointer transition-colors ${
									selectedReason === reason.id
										? 'border-blue-500 bg-blue-50'
										: 'border-gray-200 hover:border-gray-300'
								}`}
							>
								<div className="flex items-start gap-3">
									<input
										type="radio"
										name="reportReason"
										value={reason.id}
										checked={selectedReason === reason.id}
										onChange={() => handleReasonSelect(reason.id)}
										className="mt-1 text-blue-600 focus:ring-blue-500"
									/>
									<div className="flex-1">
										<p className="font-medium text-gray-900">
											{reason.label}
										</p>
										<p className="text-sm text-gray-500">
											{reason.description}
										</p>
									</div>
								</div>
							</label>
						))}
					</div>

					{/* Custom Reason Input */}
					{showCustomInput && (
						<div className="mt-4">
							<label className="block text-sm font-medium text-gray-700 mb-2">
								Please specify the reason:
							</label>
							<textarea
								value={customReason}
								onChange={(e) => setCustomReason(e.target.value)}
								placeholder="Describe why you're reporting this review..."
								className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
								rows={3}
								maxLength={500}
							/>
							<p className="text-xs text-gray-500 mt-1">
								{customReason.length}/500 characters
							</p>
						</div>
					)}

					{/* Warning */}
					<div className="mt-6 p-4 bg-amber-50 border border-amber-200 rounded-lg">
						<div className="flex items-start gap-3">
							<AlertTriangle className="w-5 h-5 text-amber-600 mt-0.5" />
							<div>
								<p className="text-sm font-medium text-amber-800">
									Important Notice
								</p>
								<p className="text-sm text-amber-700 mt-1">
									False reports may result in restrictions on your account. 
									Only report reviews that genuinely violate our community guidelines.
								</p>
							</div>
						</div>
					</div>
				</div>

				{/* Footer */}
				<div className="flex items-center justify-end gap-3 p-6 border-t bg-gray-50">
					<Button
						variant="outline"
						onClick={onClose}
						disabled={isLoading}
					>
						Cancel
					</Button>
					<Button
						onClick={handleSubmit}
						disabled={isSubmitDisabled}
						loading={isLoading}
						className="bg-red-600 hover:bg-red-700 text-white"
					>
						{isLoading ? "Reporting..." : "Report Review"}
					</Button>
				</div>
			</div>
		</div>
	);
};

export default ReportReviewModal;
