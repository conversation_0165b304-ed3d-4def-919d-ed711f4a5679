import React, { useState } from "react";
import Navbar from "../../components/navbar/navbar";
import { CustomSelect } from "../../components/select/select";
import MultiSelect from "../../components/select/multiSelect";
import { ChevronDownIcon } from "lucide-react";
import TutorCard from "./components/tutorCard/tutorCard";
import PriceDropdown from "./components/priceDropdown/priceDropdown";
import AvailabilityDropdown from "./components/availabilityDropdown/availabilityDropdown";
import MobileFilter from "./components/mobile/MobileFilter";
import MobileSearch from "./components/mobile/MobileSearch";
import searchImg from "../../assets/svgs/findtutor/search-02.svg";
import filterImg from "../../assets/svgs/findtutor/filter-horizontal.svg";
import worksImg from "../../assets/svgs/findtutor/work.svg";
import message from "../../assets/svgs/findtutor/message.svg";
import chartUp from "../../assets/svgs/findtutor/chart-up.svg";
import calender from "../../assets/svgs/findtutor/calendar.svg";
import saleTag from "../../assets/svgs/findtutor/sale-tag.svg";
import selectCountry from "../../assets/svgs/findtutor/select-country.svg";
import starAward from "../../assets/svgs/findtutor/star-award.svg";
import ApleIntelligence from "../../assets/svgs/findtutor/apple-intelligence.svg";
import { useGetTutorsQuery } from "@/redux/slices/student/findTutorApiSlice";
import Loader from "@/components/loader/loader";
import { useForm } from "react-hook-form";

const industries = [
  "Business & Corporate English",
  "Medical & Healthcare English",
  "Legal & Compliance English",
  "English for Tech & IT Professionals",
  "English for Finance, Accounting & Banking",
  "Aviation English",
  "English for Tourism & Hospitality",
  "English for Academic & Research Purposes",
  "English for Skilled Trades",
  "English for Logistics, Transport & Supply Chain",
  "English for Creative Professionals",
  "English for Media & Communication",
  "English for International Relations & NGOs",
  "English for Government & Public Sector",
  "English for Teachers & Educators",
  "English for Performing Arts & Entertainment",
  "English for Digital & Remote Work",
  "English for Real Estate & Property Professionals",
  "English for Culinary & Food Industry",
];
const specialities = [
  "Accent reduction",
  "Phonetics",
  "Listening and Reading comprehension",
  "Conversational fluency",
  "Grammar and Vocabulary focus",
  "Writing",
];

const qualities = [
  "Excellent materials",
  "Tech savvy",
  "Motivation guru",
  "Great conversation",
  "Fun",
  "Great for beginners",
  "Great for intermediate",
  "Great for advanced",
  "Cultural insights",
];

const languageLevels = [
  "Beginners (CEFR-level A1)",
  "Pre-intermediate (CEFR-level A2)",
  "Intermediate (CEFR-level-B1)",
  "Upper-intermediate (CEFR-level-B2)",
  "Advanced (CEFR-level- C1)",
  "Native (CEFR-level-C2)",
];

const countries = ["Nigeria", "America", "Canada"];
const alsoSpeaks = ["German", "Arabic", "Mandarin", "Spanish"];

const availabilityTimes = ["Morning", "Afternoon", "Evening"];
const daysOfWeek = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];

const FindTutors = () => {
  const [showFilter, setShowFilter] = React.useState(false);
  const [showSearch, setShowSearch] = React.useState(false);
  const [searchInput, setSearchInput] = useState("");
  const [filters, setFilters] = React.useState({});

  const { control } = useForm();

  // Handle input change for search
  const handleInputChange = (e) => {
    setSearchInput(e.target.value);
  };

  // Fetch tutors
  const {
    data: tutorsResponse,
    isLoading,
    refetch,
    error,
  } = useGetTutorsQuery();

  // console.log("Original tutors data:", tutorsResponse.data);

  // main filter function
  const filteredTutors = React.useMemo(() => {
    if (
      !tutorsResponse ||
      !tutorsResponse.success ||
      !Array.isArray(tutorsResponse.data)
    ) {
      console.log("No tutors data available");
      return [];
    }

    return tutorsResponse.data.filter((tutor) => {
      const matchesCountry = filters.country
        ? tutor?.country?.toLowerCase() === filters.country.toLowerCase()
        : true;

      const matchesIndustry = filters.industry
        ? tutor?.industry?.toLowerCase() === filters.industry.toLowerCase()
        : true;

      const matchesAvailability = filters.availability
        ? tutor?.availability?.some((a) =>
            a.toLowerCase().includes(filters.availability.toLowerCase())
          )
        : true;

      const matchesPrice =
        (!filters.minPrice || tutor.price >= filters.minPrice) &&
        (!filters.maxPrice || tutor.price <= filters.maxPrice);

      const matchesLanguage = filters.language
        ? tutor?.languages?.some(
            (lang) =>
              lang?.name?.toLowerCase() === filters.language.toLowerCase()
          )
        : true;

      const matchesSpeciality = filters.speciality
        ? tutor?.teachingSubjects?.some((subject) =>
            subject?.specialities?.some(
              (s) => s.toLowerCase() === filters.speciality.toLowerCase()
            )
          )
        : true;

      const matchesQualities = filters.qualitiy
        ? tutor?.qualities?.some((quality) =>
            filters.qualitiy.includes(quality.toLowerCase())
          )
        : true;

      const matchesExperienceLevel = filters.experienceLevel
        ? tutor?.languages?.some(
            (lang) =>
              lang?.level?.toLowerCase() ===
              filters.experienceLevel.toLowerCase()
          )
        : true;

      const matchesSearch = tutor?.firstname
        ?.toLowerCase()
        .includes(searchInput.toLowerCase());

      return (
        matchesSearch &&
        matchesLanguage &&
        matchesSpeciality &&
        matchesCountry &&
        matchesQualities &&
        matchesExperienceLevel &&
        matchesIndustry &&
        matchesAvailability &&
        matchesPrice
      );
    });
  }, [tutorsResponse, searchInput, filters]);
  // onchange function to
  const handleFilterChange = (key, value) => {
    setFilters((prevFilters) => {
      if (!value) {
        const updated = { ...prevFilters };
        delete updated[key];
        return updated;
      }
      return {
        ...prevFilters,
        [key]: value.toLowerCase(),
      };
    });
  };

  const clearAllFilters = () => {
    setFilters({});
    setSearchInput("");
  };

  const applyFilters = () => {
    console.log("Filters applied:", filters);
    setShowFilter(false); // close mobile filter modal
  };

  // Render loading state
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader />
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        Error loading tutors: {error.message}
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      <Navbar />
      {/* Main content container */}
      <div className="mx-auto px-4 sm:px-8 pt-4">
        {/* Mobile filters */}
        <div className="sm:hidden">
          <p className="font-bold text-xl mb-3">Filter tutors by</p>
          <div className="flex border-[#E8E8E8] items-center h-[50px] text-[#A4A4A4] gap-[12px]">
            <div className="flex-1 w-[258px] border-[1px] flex rounded-md p-[8px] gap-2">
              <img
                src={worksImg}
                alt="works img"
                width={24}
                height={24}
                className="pb-2"
              />
              <CustomSelect
                placeholder="Industry"
                label=""
                className="border-none items-center h-[27px]"
                options={industries.map((industry) => ({
                  value: industry.toLowerCase(),
                  label: industry,
                }))}
                name="industry"
                control={control}
                isRequired={false}
                onChange={(selected) =>
                  handleFilterChange("industry", selected?.value)
                }
              />
            </div>
            <button
              onClick={() => setShowFilter(true)}
              className="flex-1 sm:flex-none w-[117px] flex items-center justify-between rounded-md p-[10px] border-[1px] gap-2"
            >
              {/* Left side: image + label */}
              <div className="flex items-center gap-2">
                <img
                  src={filterImg}
                  alt="filterImg img"
                  width={24}
                  height={24}
                  className="pb-1"
                />
                <span>Filter</span>
              </div>
            </button>

            <button
              onClick={() => setShowSearch(true)}
              className="flex-none w-[50px] sm:w-[100px] flex justify-center rounded-md p-[10px] border-[1px] h-[50px]"
            >
              <img
                src={searchImg}
                alt="works img"
                width={24}
                height={24}
                className=""
              />
            </button>
          </div>

          {/* Mobile filter overlay */}

          {showFilter && (
            <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-start">
              <div className="min-h-full bg-white w-full p-4 animate-slideFromTop">
                <MobileFilter
                  onClose={() => setShowFilter(false)}
                  currentFilters={filters}
                  filters={filters}
                  handleFilterChange={handleFilterChange}
                  onClearFilters={clearAllFilters}
                  searchInput={searchInput}
                  onSearch={(value) => setSearchInput(value)}
                  onApplyFilters={applyFilters}
                />
              </div>
            </div>
          )}

          {/* Mobile search overlay */}
          {showSearch && (
            <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-start">
              <div className="min-h-full bg-white w-full p-4 animate-slideFromTop">
                <MobileSearch
                  onClose={() => setShowSearch(false)}
                  searchQuery={searchInput}
                  onInputChange={handleInputChange}
                  onSearch={(value) => setSearchInput(value)}
                />
              </div>
            </div>
          )}

          {/* Mobile results count */}
          <br />
          <div className="flex items-center justify-between my-5">
            <h4 className="text-xl text-secondary font-bold">
              {filteredTutors.length} tutor(s) found
            </h4>
            <div className="flex items-center gap-[3px]">
              <span className="text-[#A4A4A4] text-base">Sort by</span>
              <ChevronDownIcon className="w-5 h-5 text-[#A4A4A4]" />
            </div>
          </div>
        </div>

        {/* Desktop filters */}
        <div className="hidden sm:block">
          <p className="font-bold text-xl mb-3">Filter tutors by</p>

          {/* Single responsive grid that adjusts to screen size */}
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 mb-6">
            {/* Industry */}
            <div className="flex items-center h-[50px] border-[1px] focus:ring-2 focus:ring-primary p-[10px] hover:border-black border-gray-300 rounded-md">
              <div>
                <img src={worksImg} alt="industry icon" />
              </div>
              <CustomSelect
                placeholder="Industry"
                label=""
                className="border-none"
                options={industries.map((industry) => ({
                  value: industry.toLowerCase(),
                  label: industry,
                }))}
                name="industry"
                control={control}
                isRequired={false}
                onChange={(selected) =>
                  handleFilterChange("industry", selected?.value)
                }
              />
            </div>

            {/* Country */}
            <div className="flex items-center gap-2 h-[50px] border-[1px] focus:ring-2 focus:ring-primary p-[10px] hover:border-black border-gray-300 rounded-md">
              <div>
                <img
                  src={selectCountry}
                  alt="Languages Icon"
                  height={24}
                  width={24}
                />
              </div>
              <MultiSelect
                options={countries.map((country) => ({
                  label: country,
                  value: country.toLowerCase().substring(0, 3),
                }))}
                placeholder="Select country"
                buttonClassName="border-none"
                control={control}
                name="country"
                onChange={(selected) =>
                  handleFilterChange(
                    "country",
                    selected?.map((item) => item.value)
                  )
                }
              />
            </div>

            {/* Price */}
            <div className="flex items-center gap-2 h-[50px] border-[1px] focus:ring-2 focus:ring-primary p-[10px] hover:border-black border-gray-300 rounded-md">
              <div>
                <img src={saleTag} alt="price icon" height={24} width={24} />
              </div>
              <PriceDropdown
                className="border-none"
                onChange={(selected) =>
                  handleFilterChange("price", selected?.value)
                }
              />
            </div>

            {/* Availability */}
            <div className="flex items-center h-[50px] border-[1px] focus:ring-2 focus:ring-primary p-[10px] hover:border-black border-gray-300 rounded-md">
              <div>
                <img src={calender} alt="Availability Time icon" />
              </div>
              <AvailabilityDropdown
                times={availabilityTimes}
                days={daysOfWeek}
                className="border-none"
                onChange={(selected) =>
                  handleFilterChange("available", selected?.value)
                }
              />
            </div>

            {/* Also Speaks */}
            <div className="flex items-center h-[50px] border-[1px] focus:ring-2 focus:ring-primary p-[10px] hover:border-black border-gray-300 rounded-md">
              <div>
                <img src={message} alt="other language icons" />
              </div>
              <CustomSelect
                placeholder="Also Speaks"
                label=""
                className="border-none"
                options={alsoSpeaks.map((lang) => ({
                  value: lang.toLowerCase(),
                  label: lang,
                }))}
                name="speaks"
                control={control}
                isRequired={false}
                onChange={(selected) =>
                  handleFilterChange("language", selected?.value)
                }
              />
            </div>

            {/* Specialities */}
            <div className="flex items-center h-[50px] border-[1px] focus:ring-2 focus:ring-primary p-[10px] hover:border-black border-gray-300 rounded-md">
              <div>
                <img src={ApleIntelligence} alt="specialities icon" />
              </div>
              <CustomSelect
                placeholder="Specialities"
                label=""
                className="border-none"
                options={specialities.map((spec) => ({
                  value: spec.toLowerCase().replace(" ", "_"),
                  label: spec,
                }))}
                name="speciality"
                control={control}
                isRequired={false}
                onChange={(selected) =>
                  handleFilterChange("speciality", selected?.value)
                }
              />
            </div>

            {/* Qualities */}
            <div className="flex items-center h-[50px] border-[1px] focus:ring-2 focus:ring-primary p-[10px] hover:border-black border-gray-300 rounded-md">
              <div>
                <img src={starAward} alt="Qualitites icon" />
              </div>
              <MultiSelect
                options={qualities.map((quality) => ({
                  label: quality,
                  value: quality.toLowerCase().replace(" ", "_"),
                }))}
                placeholder="Select qualities"
                buttonClassName="border-none"
                control={control}
                name="qualities"
                onChange={(selected) =>
                  handleFilterChange(
                    "qualitiy",
                    selected?.map((item) => item.value)
                  )
                }
              />
            </div>

            {/* Language Level */}
            <div className="flex items-center h-[50px] border-[1px] focus:ring-2 focus:ring-primary p-[10px] hover:border-black border-gray-300 rounded-md">
              <div>
                <img src={chartUp} alt="language level " />
              </div>
              <CustomSelect
                placeholder="Language Level"
                className="border-none"
                label=""
                options={languageLevels.map((level) => ({
                  value: level.toLowerCase(),
                  label: level,
                }))}
                name="level"
                control={control}
                isRequired={false}
                onChange={(selected) =>
                  handleFilterChange("experienceLevel", selected?.value)
                }
              />
            </div>

            {/* Search by name */}
            <div className="flex items-center gap-2 border-[1px] focus:ring-2 focus:ring-primary p-[10px] hover:border-black border-gray-300 rounded-md">
              <div>
                <img src={searchImg} alt="search" />
              </div>
              <input
                type="text"
                placeholder="Search by name"
                className="focus:outline-none w-full"
                value={searchInput}
                onChange={handleInputChange}
              />
            </div>
          </div>
        </div>
        <br />

        {/* Results count and sort (desktop) */}
        <div className="hidden sm:flex items-center justify-between mb-5">
          <h3 className="text-xl text-secondary font-bold">
            {filteredTutors?.length} tutor(s) found
          </h3>
          <div className="flex items-center gap-[3px]">
            <span className="text-[#A4A4A4] text-base">Sort by</span>
            <ChevronDownIcon className="w-5 h-5 text-[#A4A4A4]" />
          </div>
        </div>
        {/* Tutor cards */}
        <div className="space-y-2">
          {filteredTutors?.length > 0 ? (
            filteredTutors?.map((tutor) => (
              <div key={tutor?.id}>
                <TutorCard tutor={tutor} />
              </div>
            ))
          ) : (
            <p className="text-gray-500 text-center">No tutors found.</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default FindTutors;
