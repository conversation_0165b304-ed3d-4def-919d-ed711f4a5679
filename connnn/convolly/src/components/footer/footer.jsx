import { Facebook, Twitter, Linkedin, Instagram } from "lucide-react";
import { <PERSON> } from "react-router-dom";
import Logo from "../../assets/svgs/logo2.svg";
import twitter from "../../assets/svgs/twitter.svg";
import facebook from "../../assets/svgs/facebook.svg";
import linkedIn from "../../assets/svgs/linkedIn.svg";
import instagram from "../../assets/svgs/instagram.svg";
import { Button } from "../button/button";

const Footer = () => {
  const currentYear = new Date().getFullYear();

  const socials = [
    {
      name: "facebook",
      icon: facebook
    },
    {
      name: "twitter",
      icon: twitter
    },
    {
      name: "linkedIn",
      icon: linkedIn
    },
    {
      name: "instagram",
      icon: instagram
    }
  ];

  return (
    <footer className="bg-[#111827] text-gray-300 py-10 sm:px-5 px-2">
      <div className="p-4 mx-auto">
        {/* Top section - adjusted widths */}
        <div className="flex flex-col md:flex-row mb-8 md:justify-between">
          {/* Logo section - now takes almost half the screen */}
          <div className="grow mb-6 md:mb-0 pr-4 max-w-[573px]">
            <div className="flex items-center mb-3">
              <div className="mb-3 text-white">
                <img
                  src={Logo}
                  className="h-[46px] w-auto "
                  alt="Convolly Logo"
                />
              </div>
              <span className="text-white text-xl font-bold"></span>
            </div>

            <p className="sm:text-lg mb-4 text-[#9CA3AF]">
              Convolly connects professionals with language tutors who
              understand their industry, helping them communicate effectively in
              a global business environment.
            </p>

            <div className="flex space-x-3">
              {socials?.map(({ name, icon }) => (
                <Link
                  to="#"
                  className="bg-[#1F2937] hover:bg-[#39485d] h-10 w-10 flex justify-center items-center rounded-full"
                >
                  <img src={icon} alt="social icon" />
                </Link>
              ))}
            </div>
          </div>

          {/* Links section - takes remaining space */}
          <div className="md:w-1/2 text-xl grid grid-cols-1 md:grid-cols-3 gap-6 text-[#9CA3AF] sm:text-lg">
            {/* Company links */}
            <div>
              <h3 className="text-white sm:text-xl text-lg font-bold mb-2">
                Company
              </h3>
              <ul className="space-y-2">
                <li>
                  <Link to="#" className="hover:text-white transition-colors">
                    About Us
                  </Link>
                </li>
                <li>
                  <Link to="#" className="hover:text-white transition-colors">
                    Blog
                  </Link>
                </li>
                <li>
                  <Link to="#" className="hover:text-white transition-colors">
                    Partners
                  </Link>
                </li>
              </ul>
            </div>

            {/* Support links */}
            <div>
              <h3 className="text-white sm:text-xl text-lg font-bold mb-2">
                Support
              </h3>
              <ul className="space-y-2">
                <li>
                  <Link to="#" className="hover:text-white transition-colors">
                    Help Center
                  </Link>
                </li>
                <li>
                  <Link to="#" className="hover:text-white transition-colors">
                    Contact Us
                  </Link>
                </li>
                <li>
                  <a to="#" className="hover:text-white transition-colors">
                    FAQs
                  </a>
                </li>
              </ul>
            </div>

            {/* Legal links */}
            <div>
              <h3 className="text-white sm:text-xl text-lg font-bold mb-2">
                Legal
              </h3>
              <ul className="space-y-2">
                <li>
                  <Link to="#" className="hover:text-white transition-colors">
                    Terms of Service
                  </Link>
                </li>
                <li>
                  <Link to="#" className="hover:text-white transition-colors">
                    Privacy Policy
                  </Link>
                </li>
                <li>
                  <Link to="#" className="hover:text-white transition-colors">
                    Cookie Policy
                  </Link>
                </li>
                <li>
                  <Link to="#" className="hover:text-white transition-colors">
                    Accessibility
                  </Link>
                </li>
                <li>
                  <Link to="#" className="hover:text-white transition-colors">
                    Security
                  </Link>
                </li>
              </ul>
            </div>
          </div>
        </div>

        <hr className="border-gray-800 my-6" />

        {/* Newsletter section */}
        <div className="mb-10 w-full md:max-w-[50%] md:pl-0">
          <h3 className="text-white sm:text-xl text-lg font-bold mb-3">
            Subscribe to Our Newsletter
          </h3>
          <div className="flex flex-col sm:flex-row">
            <input
              type="email"
              placeholder="Your email address"
              className="bg-gray-800 text-white px-3 py-2 rounded-lg rounded-tr-none rounded-br-none w-full sm:w-[400px] text-sm outline-none max-md:mb-5"
            />



            <Button className="w-[130px] text-white h-[45px] sm:text-xl bg-primary md:rounded-tl-none md:rounded-bl-none transition-colors">
              Subscribe
            </Button>
          </div>
        </div>

        {/* Copyright */}
        <div className="text-center text-[#6B7280] sm:text-lg text-xs">
          © {currentYear} Convolly. All rights reserved.
        </div>
      </div>
    </footer>
  );
};

export default Footer;
