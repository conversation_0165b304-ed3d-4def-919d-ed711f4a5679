import React, { useState, useRef, useEffect } from "react";
import { ChevronDownIcon } from "lucide-react";
import { useController } from "react-hook-form";

const MultiSelect = ({
	options = [],
	name,
	control,
	rules = {},
	placeholder = "Select options",
	label = "",
	parentClassName = "",
	buttonClassName = "",
}) => {
	const {
		field: { value, onChange },
		fieldState: { error },
	} = useController({
		name,
		control,
		rules,
		defaultValue: [],
	});

	const dropdownRef = useRef(null);
	const [isDropdownOpen, setIsDropdownOpen] = useState(false);

	useEffect(() => {
		const handleClickOutside = (event) => {
			if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
				setIsDropdownOpen(false);
			}
		};
		document.addEventListener("mousedown", handleClickOutside);
		return () => document.removeEventListener("mousedown", handleClickOutside);
	}, []);

	const handleOptionChange = (optionValue) => {
		const newValue = value.includes(optionValue)
			? value.filter((v) => v !== optionValue)
			: [...value, optionValue];
		onChange(newValue);
	};

	return (
		<div
			className={`relative w-full space-y-1 ${parentClassName}`}
			ref={dropdownRef}
		>
			{label && <p className="max-sm:text-sm text-secondary mb-1">{label}</p>}

			<button
				type="button"
				className={`border border-[#E8E8E8] px-3 py-[12px] bg-white rounded-md w-full text-left text-sm flex justify-between items-center overflow-auto ${
					value.length > 0 ? "text-black" : "text-[#777777]"
				} ${buttonClassName}`}
				onClick={() => setIsDropdownOpen(!isDropdownOpen)}
			>
				<span className="truncate">
					{value.length > 0
						? options
								.filter((opt) => value.includes(opt.value))
								.map((opt) => opt.label)
								.join(", ")
						: placeholder}
				</span>
				<ChevronDownIcon className="w-4 h-4 ml-2 text-gray-500 shrink-0" />
			</button>

			{isDropdownOpen && (
				<div className="absolute mt-1 bg-white border border-[#E8E8E8] w-full z-10 max-h-60 overflow-auto rounded-md">
					{options.map(({ label, value: val }) => (
						<label
							key={val}
							className="flex items-center gap-2 hover:bg-[#e8f9f2] p-[10px] cursor-pointer px-4"
						>
							<input
								type="checkbox"
								checked={value.includes(val)}
								onChange={() => handleOptionChange(val)}
								className="custom-checkbox text-sm border border-[#E8E8E8] bg-white rounded-xl"
							/>
							<span className="text-sm">{label}</span>
						</label>
					))}
				</div>
			)}

			{error && <p className="text-sm text-red-500 mt-1">{error.message}</p>}
		</div>
	);
};

export default MultiSelect;
