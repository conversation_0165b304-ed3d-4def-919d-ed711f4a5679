import { generalApiSlice } from "../../apiSlice";

const tutorReviewsApiSlice = generalApiSlice.injectEndpoints({
	endpoints: (builder) => ({
		// Get reviews for the current tutor
		getTutorReviews: builder.query({
			query: ({ page = 1, limit = 20, flagged, sortBy = "createdAt", order = "desc" } = {}) => ({
				url: `/api/profile/tutors/reviews`,
				method: "GET",
				params: { page, limit, flagged, sortBy, order },
			}),
			providesTags: ['TutorReview'],
		}),

		// Get reviews on a specific tutor (for public viewing)
		getReviewsOnTutor: builder.mutation({
			query: (body = {}) => ({
				url: `/api/reviews/on-me/${body.userId || ''}`,
				method: "POST",
				body: {
					page: body.page,
					cursor: body.cursor,
					limit: body.limit || 10,
					sortOrder: body.sortOrder || "desc",
				},
			}),
			providesTags: ['TutorReview'],
		}),

		// Report a review as inappropriate
		reportReview: builder.mutation({
			query: ({ reviewId, reason }) => ({
				url: `/api/profile/tutors/reviews/${reviewId}/report`,
				method: "POST",
				body: { reason },
			}),
			invalidatesTags: ['TutorReview'],
		}),

		// Respond to a review (if this feature is needed)
		respondToReview: builder.mutation({
			query: ({ reviewId, response }) => ({
				url: `/api/profile/tutors/reviews/${reviewId}/respond`,
				method: "POST",
				body: { response },
			}),
			invalidatesTags: ['TutorReview'],
		}),
	}),

	overrideExisting: false,
});

export const {
	useGetTutorReviewsQuery,
	useGetReviewsOnTutorMutation,
	useReportReviewMutation,
	useRespondToReviewMutation,
} = tutorReviewsApiSlice;

// Legacy export for backward compatibility
export const useGetReviewsQuery = tutorReviewsApiSlice.endpoints.getTutorReviews.useQuery;
