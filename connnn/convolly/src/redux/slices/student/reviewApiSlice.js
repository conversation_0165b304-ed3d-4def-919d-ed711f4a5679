import { generalApiSlice } from "../../apiSlice";

const reviewApiSlice = generalApiSlice.injectEndpoints({
	endpoints: (builder) => ({
		// Get reviews created by the current user
		getMyReviews: builder.mutation({
			query: (body = {}) => ({
				url: `/api/reviews/by-me/${body.userId || ''}`,
				method: "POST",
				body: {
					page: body.page,
					cursor: body.cursor,
					limit: body.limit || 10,
					sortOrder: body.sortOrder || "desc",
				},
			}),
			providesTags: ['Review'],
		}),

		// Get reviews on a specific user (tutor)
		getReviewsOnTutor: builder.mutation({
			query: (body = {}) => ({
				url: `/api/reviews/on-me/${body.userId || ''}`,
				method: "POST",
				body: {
					page: body.page,
					cursor: body.cursor,
					limit: body.limit || 10,
					sortOrder: body.sortOrder || "desc",
				},
			}),
			providesTags: ['Review'],
		}),

		// Create a new review
		createReview: builder.mutation({
			query: (body) => ({
				url: `/api/reviews/create/${body.targetId}`,
				method: "POST",
				body: {
					rating: body.rating,
					comment: body.comment,
					targetRole: body.targetRole || 'tutor',
					lessonId: body.lessonId,
				},
			}),
			invalidatesTags: ['Review'],
		}),

		// Update an existing review
		updateReview: builder.mutation({
			query: (body) => ({
				url: `/api/reviews/${body.reviewId}`,
				method: "PUT",
				body: {
					rating: body.rating,
					comment: body.comment,
				},
			}),
			invalidatesTags: ['Review'],
		}),

		// Delete a review
		deleteReview: builder.mutation({
			query: ({ reviewId }) => ({
				url: `/api/reviews/${reviewId}`,
				method: "DELETE",
			}),
			invalidatesTags: ['Review'],
		}),
	}),

	overrideExisting: false,
});

export const {
	useGetMyReviewsMutation,
	useGetReviewsOnTutorMutation,
	useCreateReviewMutation,
	useUpdateReviewMutation,
	useDeleteReviewMutation,
} = reviewApiSlice;

// Legacy export for backward compatibility
export const useReviewTutorMutation = useCreateReviewMutation;
